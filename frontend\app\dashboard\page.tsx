"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  LogOut,
  User,
  Menu,
  X,
  Activity,
  ChevronLeft,
  ChevronRight,
  Users,
  Loader2,
  AlertCircle,
  Home,
  Brain,
  BarChart3,
  LineChart,
} from "lucide-react"

// Enhanced mock data for realistic demonstration
const agents = [
  {
    id: "agent1",
    name: "智能客服助手",
    url: "https://example.com/agent1",
    type: "客服",
    status: "在线",
    lastActive: "2分钟前",
    tasksCompleted: 127,
    successRate: 98.5
  },
  {
    id: "agent2",
    name: "数据分析专家",
    url: "https://example.com/agent2",
    type: "分析",
    status: "忙碌",
    lastActive: "刚刚",
    tasksCompleted: 89,
    successRate: 96.2
  },
  {
    id: "agent3",
    name: "内容创作助理",
    url: "https://example.com/agent3",
    type: "创作",
    status: "在线",
    lastActive: "5分钟前",
    tasksCompleted: 156,
    successRate: 94.8
  },
  {
    id: "agent4",
    name: "代码审查机器人",
    url: "https://example.com/agent4",
    type: "开发",
    status: "离线",
    lastActive: "1小时前",
    tasksCompleted: 73,
    successRate: 99.1
  },
]

const dashboardStats = [
  {
    title: "总代理",
    value: "4",
    subtitle: "3个在线",
    color: "bg-blue-500",
    trend: "+2",
    trendDirection: "up"
  },
  {
    title: "CPU使用率",
    value: "42.8%",
    subtitle: "平均负载",
    color: "bg-green-500",
    trend: "-5.2%",
    trendDirection: "down"
  },
  {
    title: "内存使用",
    value: "67.3%",
    subtitle: "8.2GB / 12GB",
    color: "bg-purple-500",
    trend: "+3.1%",
    trendDirection: "up"
  },
  {
    title: "今日任务",
    value: "445",
    subtitle: "已完成",
    color: "bg-orange-500",
    trend: "+28",
    trendDirection: "up"
  },
]

const agentActivity = [
  {
    id: 1,
    agent: "智能客服助手",
    action: "处理用户咨询",
    status: "已完成",
    time: "刚刚",
    color: "bg-green-500",
    duration: "2分钟"
  },
  {
    id: 2,
    agent: "数据分析专家",
    action: "生成销售报告",
    status: "进行中",
    time: "3分钟前",
    color: "bg-blue-500",
    duration: "预计5分钟"
  },
  {
    id: 3,
    agent: "内容创作助理",
    action: "撰写产品描述",
    status: "已完成",
    time: "5分钟前",
    color: "bg-green-500",
    duration: "8分钟"
  },
  {
    id: 4,
    agent: "智能客服助手",
    action: "更新知识库",
    status: "已完成",
    time: "8分钟前",
    color: "bg-green-500",
    duration: "3分钟"
  },
  {
    id: 5,
    agent: "代码审查机器人",
    action: "代码质量检查",
    status: "等待中",
    time: "12分钟前",
    color: "bg-yellow-500",
    duration: "待处理"
  },
]

// Performance metrics for charts
const performanceData = {
  systemLoad: [
    { time: "00:00", cpu: 35, memory: 62, network: 45 },
    { time: "04:00", cpu: 28, memory: 58, network: 38 },
    { time: "08:00", cpu: 42, memory: 65, network: 52 },
    { time: "12:00", cpu: 48, memory: 71, network: 61 },
    { time: "16:00", cpu: 38, memory: 67, network: 47 },
    { time: "20:00", cpu: 33, memory: 63, network: 42 },
  ],
  agentPerformance: [
    { name: "智能客服助手", completed: 127, success: 98.5, avgTime: 3.2 },
    { name: "数据分析专家", completed: 89, success: 96.2, avgTime: 12.5 },
    { name: "内容创作助理", completed: 156, success: 94.8, avgTime: 8.7 },
    { name: "代码审查机器人", completed: 73, success: 99.1, avgTime: 15.3 },
  ],
  taskDistribution: [
    { category: "客服咨询", count: 185, percentage: 41.6 },
    { category: "数据分析", count: 89, percentage: 20.0 },
    { category: "内容创作", count: 156, percentage: 35.1 },
    { category: "代码审查", count: 15, percentage: 3.3 },
  ]
}

export default function DashboardPage() {
  const [selectedView, setSelectedView] = useState<string>("home") // Changed from selectedAgent to selectedView for home/agent views
  const [username, setUsername] = useState("")
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [touchStartX, setTouchStartX] = useState<number | null>(null)
  const [touchCurrentX, setTouchCurrentX] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [iframeError, setIframeError] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn")
    const storedUsername = localStorage.getItem("username")

    if (!isLoggedIn) {
      router.push("/")
      return
    }

    if (storedUsername) {
      setUsername(storedUsername)
    }
  }, [router])

  // Enhanced mobile menu accessibility and keyboard support
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    const handleResize = () => {
      // Close mobile menu when switching to desktop view
      if (window.innerWidth >= 1024 && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    window.addEventListener('resize', handleResize)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('resize', handleResize)
    }
  }, [mobileMenuOpen])

  const handleLogout = () => {
    localStorage.removeItem("isLoggedIn")
    localStorage.removeItem("username")
    router.push("/")
  }

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  // Enhanced touch handling for mobile menu
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX)
    setTouchCurrentX(e.touches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartX === null) return
    setTouchCurrentX(e.touches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (touchStartX === null || touchCurrentX === null) return

    const deltaX = touchCurrentX - touchStartX
    const threshold = 50 // Minimum swipe distance

    // Swipe right to open menu (when closed)
    if (deltaX > threshold && !mobileMenuOpen) {
      setMobileMenuOpen(true)
    }
    // Swipe left to close menu (when open)
    else if (deltaX < -threshold && mobileMenuOpen) {
      setMobileMenuOpen(false)
    }

    setTouchStartX(null)
    setTouchCurrentX(null)
  }

  // Close mobile menu when clicking outside
  const handleBackdropClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setMobileMenuOpen(false)
  }

  const handleViewSelect = (view: string) => {
    // Renamed from handleAgentSelect to handleViewSelect
    if (view === "home") {
      setSelectedView("home")
      setMobileMenuOpen(false)
      return
    }

    setIsLoading(true)
    setIframeError(false)
    setSelectedView(view)
    setMobileMenuOpen(false)
    setTimeout(() => setIsLoading(false), 1000)
  }



  const selectedAgentData = agents.find((agent) => agent.id === selectedView)

  return (
    <div className="min-h-screen bg-slate-50 flex">
      {/* Enhanced mobile menu backdrop with smooth fade animation */}
      <div
        className={`
          fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out
          ${mobileMenuOpen
            ? "bg-black bg-opacity-50 backdrop-blur-sm visible"
            : "bg-transparent invisible"
          }
        `}
        onClick={handleBackdropClick}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      />

      <aside
        className={`
          ${mobileMenuOpen ? "translate-x-0 shadow-2xl" : "-translate-x-full shadow-none"}
          lg:translate-x-0 lg:shadow-none
          fixed lg:relative
          z-50 lg:z-auto
          bg-slate-900
          border-r border-slate-700
          flex flex-col
          transition-all duration-300 ease-out
          ${sidebarCollapsed ? "lg:w-16" : "lg:w-80"}
          w-80
          h-screen
          lg:transform-none
        `}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className={`border-b border-slate-700 ${sidebarCollapsed ? 'p-2' : 'p-4'}`}>
          <div className={`flex items-center mb-4 ${sidebarCollapsed ? 'justify-center' : 'justify-between'}`}>
            {!sidebarCollapsed && <h1 className="text-xl font-semibold text-white">Agent 管理系统</h1>}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleSidebar}
                className="hidden lg:flex text-slate-400 hover:bg-slate-800 hover:text-white"
              >
                {sidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMobileMenu}
                className="lg:hidden text-slate-400 hover:bg-slate-800 hover:text-white transition-all duration-200 hover:scale-110 active:scale-95"
              >
                <div className="transition-transform duration-200 hover:rotate-90">
                  <X className="h-4 w-4" />
                </div>
              </Button>
            </div>
          </div>

          {/* User profile section - show avatar only when collapsed */}
          {sidebarCollapsed ? (
            <div className="flex justify-center mb-4">
              <div
                className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-colors"
                title={`${username || "手系 Agent"} - 管理员`}
              >
                <User className="h-4 w-4 text-white" />
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-3 p-3 bg-slate-800 rounded-lg mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{username || "手系 Agent"}</p>
                <p className="text-xs text-slate-400">管理员</p>
              </div>
            </div>
          )}
        </div>

        <nav className={`flex-1 ${sidebarCollapsed ? 'p-2' : 'p-4'}`}>
          <div className="flex items-center justify-between mb-4">
            {!sidebarCollapsed && (
              <h2 className="text-sm font-semibold text-slate-400 uppercase tracking-wider">Agents</h2>
            )}
          </div>

          <ul className="space-y-2">
            <li>
              <button
                onClick={() => handleViewSelect("home")}
                className={`
                  w-full flex items-center rounded-lg text-sm font-medium
                  transition-all duration-200 min-h-[44px]
                  ${sidebarCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-3 py-3'}
                  ${
                    selectedView === "home"
                      ? "bg-blue-600 text-white shadow-lg"
                      : "text-slate-300 hover:bg-slate-800 hover:text-white"
                  }
                `}
                title={sidebarCollapsed ? "首页" : undefined}
              >
                <Home className="h-5 w-5 flex-shrink-0" />
                {!sidebarCollapsed && <span>首页</span>}
                {!sidebarCollapsed && selectedView === "home" && (
                  <div className="ml-auto w-2 h-2 bg-white rounded-full" />
                )}
              </button>
            </li>

            {/* Agent category header */}
            <li className="pt-4">
              {!sidebarCollapsed && (
                <div className="px-3 py-2">
                  <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wider">AI 专家</h3>
                </div>
              )}
            </li>

            {agents.map((agent) => (
              <li key={agent.id}>
                <button
                  onClick={() => handleViewSelect(agent.id)}
                  className={`
                    w-full flex items-center rounded-lg text-sm font-medium
                    transition-all duration-200 min-h-[44px]
                    ${sidebarCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-3 py-3'}
                    ${
                      selectedView === agent.id
                        ? "bg-blue-600 text-white shadow-lg"
                        : "text-slate-300 hover:bg-slate-800 hover:text-white"
                    }
                  `}
                  title={sidebarCollapsed ? agent.name : undefined}
                >
                  <Brain className="h-5 w-5 flex-shrink-0" />
                  {!sidebarCollapsed && <span>{agent.name}</span>}
                  {!sidebarCollapsed && selectedView === agent.id && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full" />
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        <div className={`border-t border-slate-700 ${sidebarCollapsed ? 'p-2' : 'p-4'}`}>
          <Button
            variant="ghost"
            onClick={handleLogout}
            className={`
              w-full text-slate-300 hover:bg-slate-800 hover:text-white
              ${sidebarCollapsed ? "justify-center px-2" : "justify-start px-3"}
            `}
            title={sidebarCollapsed ? "退出登录" : undefined}
          >
            <LogOut className="h-4 w-4" />
            {!sidebarCollapsed && <span className="ml-2">退出登录</span>}
          </Button>
        </div>
      </aside>

      {/* Enhanced mobile menu button with animation and better visual feedback */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMobileMenu}
        className={`
          lg:hidden fixed top-4 left-4 z-30
          bg-slate-900/90 backdrop-blur-sm text-white
          hover:bg-slate-800 active:bg-slate-700
          border border-slate-700/50
          transition-all duration-200 ease-in-out
          hover:scale-105 active:scale-95
          shadow-lg hover:shadow-xl
          ${mobileMenuOpen ? 'bg-slate-800 scale-105' : ''}
        `}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className={`transition-transform duration-200 ${mobileMenuOpen ? 'rotate-90' : ''}`}>
          <Menu className="h-5 w-5" />
        </div>
      </Button>

      <main className="flex-1 p-4 lg:p-6 overflow-auto">
        {selectedView === "home" ? ( // Changed condition to check for home view
          <div className="space-y-6">
            {/* Welcome Header */}
            <div className="bg-white rounded-lg border border-slate-200 p-6">
              <h1 className="text-2xl font-bold text-slate-900 mb-2">欢迎回来，{username || "管理员"}</h1>
              <p className="text-slate-600">这里是您的 AI Agent 管理中心概览</p>
            </div>

            {/* Dashboard Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 lg:gap-6">
              {dashboardStats.map((stat, index) => (
                <Card key={index} className="bg-white border-slate-200 hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">{stat.title}</p>
                        <p className="text-2xl font-bold text-slate-900 mt-1">{stat.value}</p>
                        <p className="text-xs text-slate-500 mt-1">{stat.subtitle}</p>
                      </div>
                      <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                        <div className="w-6 h-6 bg-white rounded opacity-80"></div>
                      </div>
                    </div>
                    {stat.trend && (
                      <div className="mt-3 flex items-center">
                        <span className={`text-xs font-medium ${
                          stat.trendDirection === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {stat.trendDirection === 'up' ? '↗' : '↘'} {stat.trend}
                        </span>
                        <span className="text-xs text-slate-500 ml-1">vs 昨天</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Optimized chart layout - reduced from 3 to 2 charts for better single-page view */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {/* Combined Agent Performance and Task Distribution */}
              <Card className="bg-white border-slate-200 hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-slate-900 flex items-center gap-2 text-base">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    Agent 性能与任务分布
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {/* Agent Performance Section */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-slate-700 mb-3 flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      性能统计
                    </h4>
                    <div className="space-y-2">
                      {performanceData.agentPerformance.slice(0, 3).map((agent, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-slate-900">{agent.name}</p>
                            <p className="text-xs text-slate-500">完成: {agent.completed} | 成功率: {agent.success}%</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-bold text-blue-600">{agent.avgTime}分钟</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Task Distribution Section */}
                  <div>
                    <h4 className="text-sm font-medium text-slate-700 mb-3 flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      任务分布
                    </h4>
                    <div className="space-y-2">
                      {performanceData.taskDistribution.map((task, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${
                              index === 0 ? 'bg-blue-500' :
                              index === 1 ? 'bg-green-500' :
                              index === 2 ? 'bg-purple-500' : 'bg-orange-500'
                            }`}></div>
                            <span className="text-sm text-slate-700">{task.category}</span>
                          </div>
                          <div className="text-right">
                            <span className="text-sm font-medium text-slate-900">{task.count}</span>
                            <span className="text-xs text-slate-500 ml-1">({task.percentage}%)</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* System Load Chart */}
              <Card className="bg-white border-slate-200 hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-slate-900 flex items-center gap-2 text-base">
                    <LineChart className="h-5 w-5 text-purple-600" />
                    系统负载趋势
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {performanceData.systemLoad.slice(-4).map((load, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-600">{load.time}</span>
                          <div className="flex gap-4">
                            <span className="text-slate-900 font-medium">CPU: {load.cpu}%</span>
                            <span className="text-slate-700">内存: {load.memory}%</span>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <div className="w-full bg-slate-200 rounded-full h-2">
                            <div
                              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${load.cpu}%` }}
                            ></div>
                          </div>
                          <div className="w-full bg-slate-200 rounded-full h-1">
                            <div
                              className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                              style={{ width: `${load.memory}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Compact Recent Activity and Agent Status */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {/* Recent Activity - Showing fewer items for compact view */}
              <Card className="bg-white border-slate-200">
                <CardHeader className="pb-2">
                  <CardTitle className="text-slate-900 flex items-center gap-2 text-base">
                    <Activity className="h-5 w-5 text-orange-600" />
                    最近活动
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {agentActivity.slice(0, 4).map((activity) => (
                      <div key={activity.id} className="flex items-center gap-3 p-2 bg-slate-50 rounded-lg">
                        <div className={`w-2 h-2 rounded-full ${activity.color}`}></div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-slate-900 truncate">{activity.agent}</p>
                          <p className="text-xs text-slate-600">{activity.action}</p>
                        </div>
                        <div className="text-right">
                          <p className={`text-xs font-medium ${
                            activity.status === '已完成' ? 'text-green-600' :
                            activity.status === '进行中' ? 'text-blue-600' : 'text-yellow-600'
                          }`}>
                            {activity.status}
                          </p>
                          <p className="text-xs text-slate-500">{activity.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Agent Status - Compact view */}
              <Card className="bg-white border-slate-200">
                <CardHeader className="pb-2">
                  <CardTitle className="text-slate-900 flex items-center gap-2 text-base">
                    <Users className="h-5 w-5 text-indigo-600" />
                    Agent 状态
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {agents.map((agent) => (
                      <div key={agent.id} className="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${
                            agent.status === '在线' ? 'bg-green-500' :
                            agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400'
                          }`}></div>
                          <div>
                            <p className="text-sm font-medium text-slate-900">{agent.name}</p>
                            <p className="text-xs text-slate-500">{agent.type} • {agent.lastActive}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-slate-900">{agent.tasksCompleted}</p>
                          <p className="text-xs text-slate-500">任务完成</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <Card className="h-full bg-white border-slate-200">
            <CardContent className="h-full p-0">
              {isLoading ? (
                <div className="h-full flex items-center justify-center">
                  <div className="flex flex-col items-center gap-4">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                    <p className="text-slate-600">加载 {selectedAgentData?.name}...</p>
                  </div>
                </div>
              ) : iframeError ? (
                <div className="h-full flex items-center justify-center">
                  <div className="flex flex-col items-center gap-4 text-center">
                    <AlertCircle className="h-12 w-12 text-red-500" />
                    <div>
                      <p className="text-slate-900 font-medium">加载失败</p>
                      <p className="text-slate-600 text-sm mt-1">无法加载 {selectedAgentData?.name}</p>
                    </div>
                    <Button onClick={() => handleViewSelect(selectedView)} variant="outline" size="sm">
                      重试
                    </Button>
                  </div>
                </div>
              ) : selectedAgentData ? (
                <iframe
                  src={selectedAgentData.url}
                  className="w-full h-full border-0 rounded-lg"
                  title={selectedAgentData.name}
                  onError={() => setIframeError(true)}
                  sandbox="allow-scripts allow-same-origin allow-forms"
                />
              ) : null}
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  )
}
